#!/usr/bin/env python3
"""
Script to combine Chipotle restaurant data and menu data.
This script directly fetches restaurant and menu data from the Chipotle API.

Usage: python chipotle_combined.py --zipcode 10001 [--limit 3]
"""

import json
import sys
import argparse
import os
import time
import requests
from geopy.geocoders import Nominatim

def get_lat_long_from_zipcode(zipcode):
    """
    Convert a zipcode to latitude and longitude using Nominatim geocoder.
    """
    try:
        # Initialize the geocoder
        geolocator = Nominatim(user_agent="chipotle_finder")

        # Get location data for the zipcode
        location = geolocator.geocode(f"{zipcode}, USA")

        if location:
            print(f"Converted zipcode {zipcode} to coordinates: {location.latitude}, {location.longitude}")
            return location.latitude, location.longitude
        else:
            print(f"Could not find coordinates for zipcode {zipcode}")
            return None, None
    except Exception as e:
        print(f"Error converting zipcode to coordinates: {e}")
        return None, None

def fetch_restaurant_data(zipcode):
    """
    Fetch restaurant data directly from the Chipotle API.
    """
    print(f"Fetching restaurant data for zipcode {zipcode}...")

    # Convert zipcode to lat/long
    latitude, longitude = get_lat_long_from_zipcode(zipcode)

    if not latitude or not longitude:
        print("Failed to get coordinates for zipcode. Exiting.")
        return None

    url = "https://services.chipotle.com/restaurant/v3/restaurant"

    headers = {
        "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
    }

    payload = {
        "latitude": latitude,
        "longitude": longitude,
        "radius": 80647,  # About 50 miles in meters
        "restaurantStatuses": ["OPEN", "LAB"],
        "conceptIds": ["CMG"],
        "orderBy": "distance",
        "orderByDescending": False,
        "pageSize": 10,
        "pageIndex": 0,
        "embeds": {
            "addressTypes": ["MAIN"],
            "realHours": True,
            "directions": True,
            "catering": True,
            "onlineOrdering": True,
            "timezone": True,
            "marketing": True,
            "chipotlane": True,
            "sustainability": True,
            "experience": True
        }
    }

    try:
        print("Sending request to Chipotle API...")
        response = requests.post(url, headers=headers, json=payload)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the JSON response
        original_data = response.json()

        # Transform the data to the desired format
        simplified_data = {"data": []}

        for restaurant in original_data.get('data', []):
            # Get the first address if available
            address = restaurant.get('addresses', [{}])[0] if restaurant.get('addresses') else {}

            # Create a simplified restaurant object with only the desired fields
            simplified_restaurant = {
                "restaurantNumber": restaurant.get('restaurantNumber'),
                "restaurantName": address.get('addressLine1', restaurant.get('restaurantName', '')),
                "distance": restaurant.get('distance'),
                "postalCode": address.get('postalCode', ''),
                "administrativeArea": address.get('administrativeArea', ''),
                "latitude": address.get('latitude'),
                "longitude": address.get('longitude')
            }

            # Add the simplified restaurant to the data array
            simplified_data['data'].append(simplified_restaurant)

        return simplified_data

    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None

    except json.JSONDecodeError:
        print("Error: Could not parse the response as JSON")
        return None

def fetch_menu_metadata():
    """
    Fetch menu metadata from the Chipotle API to get thumbnail URLs.
    """
    print("Fetching menu metadata for thumbnails...")

    url = "https://services.chipotle.com/menu-metadata/v1/menu-metadata"

    # Query parameters
    params = {
        "channel": "web",
        "region": "US"
    }

    # Headers for the request
    headers = {
        "x-kpsdk-ct": "0HKt5Z9aTcNcFzMGbf8jE1ovlwu6xQWo5T7g5JgJspZdbSQgah2B6Qu5crdiRLqET1XdwsVE66oLYBneQpP1llXgJX8DHVZh0eHJiFBDMFB7C2CY2gIs9GdlvNd1qJ20zEJJeOha7DW1meJTULEsV9z58LO6IX80b5RNHfUF",
        "sec-ch-ua-platform": "\"Android\"",
        "Referer": "https://www.chipotle.com/",
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?1",
        "x-kpsdk-v": "j-1.1.0",
        "Chipotle-CorrelationId": "OrderWeb-bb874df9-fec1-4c3f-88b6-59941e4dd2b4",
        "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
        "Accept": "application/json, text/plain, */*",
        "DNT": "1",
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
    }

    try:
        print("Sending request to Chipotle API for menu metadata...")
        response = requests.get(url, headers=headers, params=params)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the JSON response
        data = response.json()

        # Create a dictionary to store item thumbnails
        thumbnail_map = {}

        # First check if we have items in the data
        if "items" in data and isinstance(data["items"], dict):
            for item_id, item_data in data["items"].items():
                if "thumbnailUrl" in item_data:
                    thumbnail_map[item_id] = {
                        "thumbnailUrl": item_data["thumbnailUrl"]
                    }

                    # Add description if available
                    if "description" in item_data:
                        thumbnail_map[item_id]["description"] = item_data["description"]

                    # Add bannerUrl if available
                    if "bannerUrl" in item_data:
                        thumbnail_map[item_id]["bannerUrl"] = item_data["bannerUrl"]

        # If no items found in the expected format, try to extract from groups
        elif "groups" in data:
            # Process each group to find items with thumbnailImageUrl
            for group in data["groups"]:
                # Get group thumbnail and banner URLs for reference
                group_thumbnail = group.get("thumbnailImageUrl", "")
                group_banner = group.get("bannerImageUrl", "")

                # Process items in this group
                if "items" in group and isinstance(group["items"], list):
                    for item_entry in group["items"]:
                        if "menuItemId" in item_entry:
                            item_id = item_entry["menuItemId"]
                            # Only add if not already in the thumbnail map
                            if item_id not in thumbnail_map:
                                thumbnail_map[item_id] = {}

                                # For now, use the group's thumbnail URL as a fallback
                                if group_thumbnail:
                                    thumbnail_map[item_id]["thumbnailUrl"] = group_thumbnail

                                # Use group's banner URL as a fallback
                                if group_banner:
                                    thumbnail_map[item_id]["bannerUrl"] = group_banner

        print(f"Successfully fetched metadata for {len(thumbnail_map)} menu items")
        return thumbnail_map

    except requests.exceptions.RequestException as e:
        print(f"Error making request for menu metadata: {e}")
        return {}

    except json.JSONDecodeError:
        print("Error: Could not parse the menu metadata response as JSON")
        return {}

def fetch_menu_data(restaurant_number):
    """
    Fetch menu data directly from the Chipotle API.
    """
    print(f"Fetching menu data for restaurant #{restaurant_number}...")

    url = f"https://services.chipotle.com/menuinnovation/v1/restaurants/{restaurant_number}/onlinemenu"

    # Query parameters
    params = {
        "channelId": "web",
        "includeUnavailableItems": "true"
    }

    # Headers from the curl command
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Chipotle-CorrelationId": "OrderWeb-00227017-1f3d-431b-945c-128db966dd09",
        "Connection": "keep-alive",
        "DNT": "1",
        "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
        "Origin": "https://www.chipotle.com",
        "Referer": "https://www.chipotle.com/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": "\"Android\"",
        "x-kpsdk-ct": "0F8T1iNJH1rIUvBuThQ1vrc7InDZxcnmfTVq9sz4k47tpWbW1pl4w5pCBoKb7nQ3AAOySbkH7FWfn41wURQJbiwsnFbrJosRHwEgpbdkkqdpqaHmnZGenbxDkULvosxNml7xLhp3RHw3BuWdlTtnk2jW3neCvHbrMYTh2n0f",
        "x-kpsdk-v": "j-1.1.0"
    }

    try:
        print(f"Sending request to Chipotle API for restaurant #{restaurant_number}...")
        response = requests.get(url, headers=headers, params=params)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the JSON response
        original_data = response.json()

        # Transform the data to match the desired format
        transformed_data = {
            "restaurantId": original_data.get("restaurantId")
        }

        # Process each section (entrees, sides, drinks, nonFoodItems)
        for section in ["entrees", "sides", "drinks", "nonFoodItems"]:
            if section in original_data:
                # Create a list to hold the simplified items for this section
                transformed_data[section] = []

                # Process each item in the section
                for item in original_data[section]:
                    # Extract only the fields we need
                    simplified_item = {
                        "itemName": item.get("itemName", ""),
                        "itemId": item.get("itemId", ""),
                        "itemCategory": item.get("itemCategory", ""),
                        "itemType": item.get("itemType", ""),
                        "unitPrice": item.get("unitPrice", 0),
                        "unitCount": item.get("unitCount", 1)
                    }

                    # Add primaryFillingName for entrees if available
                    if section == "entrees" and "primaryFillingName" in item:
                        simplified_item["primaryFillingName"] = item["primaryFillingName"]

                    # Add the simplified item to the section
                    transformed_data[section].append(simplified_item)

        return transformed_data

    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None

    except json.JSONDecodeError:
        print("Error: Could not parse the response as JSON")
        return None



def main():
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(description='Combine Chipotle restaurant and menu data')
    parser.add_argument('--zipcode', type=str, required=True,
                        help='US zipcode to search for nearby Chipotle restaurants')
    parser.add_argument('--limit', type=int, default=3,
                        help='Maximum number of restaurants to process (default: 3)')

    # Parse the arguments
    args = parser.parse_args()

    # Fetch restaurant data directly from the API
    restaurant_data = fetch_restaurant_data(args.zipcode)

    if not restaurant_data:
        print("Failed to get restaurant data. Exiting.")
        sys.exit(1)

    # Get the list of restaurants
    restaurants = restaurant_data.get("data", [])

    if not restaurants:
        print("No restaurants found. Exiting.")
        sys.exit(1)

    print(f"Found {len(restaurants)} restaurants. Processing up to {args.limit} restaurants...")

    # Fetch menu metadata to get thumbnails
    print("Fetching menu metadata for thumbnails...")
    thumbnail_data = fetch_menu_metadata()

    if not thumbnail_data:
        print("Warning: Failed to get thumbnail data. Proceeding without thumbnails.")
    else:
        print(f"Successfully fetched thumbnail data for {len(thumbnail_data)} items.")

    # Limit the number of restaurants to process
    restaurants = restaurants[:min(args.limit, len(restaurants))]

    # Create a list to hold the combined data for all restaurants
    all_combined_data = []

    # Process each restaurant
    for restaurant in restaurants:
        restaurant_number = restaurant.get("restaurantNumber")

        if not restaurant_number:
            print("Error: Restaurant number not found in restaurant data")
            continue

        # Fetch menu data directly from the API
        menu_data = fetch_menu_data(restaurant_number)

        if not menu_data:
            print(f"Failed to get menu data for restaurant #{restaurant_number}. Skipping.")
            continue

        # Create the combined data structure
        combined_data = {
            "restaurantNumber": restaurant.get("restaurantNumber"),
            "restaurantName": restaurant.get("restaurantName"),
            "distance": restaurant.get("distance"),
            "postalCode": restaurant.get("postalCode"),
            "administrativeArea": restaurant.get("administrativeArea"),
            "latitude": restaurant.get("latitude"),
            "longitude": restaurant.get("longitude"),
            "restaurantId": menu_data.get("restaurantId")
        }

        # Process each section and add thumbnails to items
        for section in ["entrees", "sides", "drinks", "nonFoodItems"]:
            if section in menu_data:
                # Create a list to hold the items for this section
                combined_data[section] = []

                # Process each item in the section
                for item in menu_data[section]:
                    # Copy the item
                    enhanced_item = item.copy()

                    # Add thumbnail URL if available
                    item_id = item.get("itemId")
                    if item_id and item_id in thumbnail_data and "thumbnailUrl" in thumbnail_data[item_id]:
                        enhanced_item["thumbnailUrl"] = thumbnail_data[item_id]["thumbnailUrl"]

                        # Add description if available
                        if "description" in thumbnail_data[item_id]:
                            enhanced_item["description"] = thumbnail_data[item_id]["description"]

                        # Add bannerUrl if available
                        if "bannerUrl" in thumbnail_data[item_id]:
                            enhanced_item["bannerUrl"] = thumbnail_data[item_id]["bannerUrl"]

                    # Add the enhanced item to the section
                    combined_data[section].append(enhanced_item)

        all_combined_data.append(combined_data)

        # Add a small delay to avoid overwhelming the API
        time.sleep(1)

    # Save the combined data to a file
    output_file = f"chipotle_combined_{args.zipcode}.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(all_combined_data, f, indent=2)

    print(f"Successfully saved combined data with thumbnails for {len(all_combined_data)} restaurants to {output_file}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
