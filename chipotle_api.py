#!/usr/bin/env python3
"""
Scrip<PERSON> to fetch Chipotle restaurant data and save it to a JSON file.
Usage: python chipotle_api.py --zipcode 10001
"""

import json
import requests
import sys
import argparse
from geopy.geocoders import Nominatim

def get_lat_long_from_zipcode(zipcode):
    """
    Convert a zipcode to latitude and longitude using Nominatim geocoder.
    """
    try:
        # Initialize the geocoder
        geolocator = Nominatim(user_agent="chipotle_finder")

        # Get location data for the zipcode
        location = geolocator.geocode(f"{zipcode}, USA")

        if location:
            print(f"Converted zipcode {zipcode} to coordinates: {location.latitude}, {location.longitude}")
            return location.latitude, location.longitude
        else:
            print(f"Could not find coordinates for zipcode {zipcode}")
            return None, None
    except Exception as e:
        print(f"Error converting zipcode to coordinates: {e}")
        return None, None

def fetch_chipotle_data(zipcode=None):
    """
    Send a request to the Chipotle API and save the response to a JSON file.
    If zipcode is provided, convert it to lat/long and use those coordinates.
    """
    url = "https://services.chipotle.com/restaurant/v3/restaurant"

    headers = {
        "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
    }

    # Default coordinates (New York City area)
    latitude = 40.81873859999999
    longitude = -73.9271644

    # If zipcode is provided, try to convert it to lat/long
    if zipcode:
        lat, lng = get_lat_long_from_zipcode(zipcode)
        if lat and lng:
            latitude = lat
            longitude = lng

    payload = {
        "latitude": latitude,
        "longitude": longitude,
        "radius": 80647,  # About 50 miles in meters
        "restaurantStatuses": ["OPEN", "LAB"],
        "conceptIds": ["CMG"],
        "orderBy": "distance",
        "orderByDescending": False,
        "pageSize": 10,
        "pageIndex": 0,
        "embeds": {
            "addressTypes": ["MAIN"],
            "realHours": True,
            "directions": True,
            "catering": True,
            "onlineOrdering": True,
            "timezone": True,
            "marketing": True,
            "chipotlane": True,
            "sustainability": True,
            "experience": True
        }
    }

    try:
        print("Sending request to Chipotle API...")
        response = requests.post(url, headers=headers, json=payload)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the JSON response
        original_data = response.json()

        # Transform the data to the desired format
        simplified_data = {"data": []}

        for restaurant in original_data.get('data', []):
            # Get the first address if available
            address = restaurant.get('addresses', [{}])[0] if restaurant.get('addresses') else {}

            # Create a simplified restaurant object with only the desired fields
            simplified_restaurant = {
                "restaurantNumber": restaurant.get('restaurantNumber'),
                "restaurantName": address.get('addressLine1', restaurant.get('restaurantName', '')),
                "distance": restaurant.get('distance'),
                "postalCode": address.get('postalCode', ''),
                "administrativeArea": address.get('administrativeArea', ''),
                "latitude": address.get('latitude'),
                "longitude": address.get('longitude')
            }

            # Add the simplified restaurant to the data array
            simplified_data['data'].append(simplified_restaurant)

        # Create the filename
        filename = "results.json"

        # Save the transformed response to a JSON file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, indent=2)

        print(f"Successfully saved simplified response to {filename}")
        print(f"Found {len(simplified_data.get('data', []))} restaurants in the response")

        return True

    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return False

    except json.JSONDecodeError:
        print("Error: Could not parse the response as JSON")
        return False

    except IOError as e:
        print(f"Error saving file: {e}")
        return False

if __name__ == "__main__":
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(description='Fetch Chipotle restaurant data based on zipcode')
    parser.add_argument('--zipcode', type=str, help='US zipcode to search for nearby Chipotle restaurants')

    # Parse the arguments
    args = parser.parse_args()

    # Call the function with the zipcode if provided
    success = fetch_chipotle_data(args.zipcode)

    sys.exit(0 if success else 1)
