import json
import re
import asyncio
import os
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, Playwright
from utils import convert_menu
from time import sleep
import aiohttp
import uuid

class ChipotlePlaywrightCrawler:
    def __init__(self, headless: bool = True, sleep_time: int = 5):
        self.headless = headless
        self.sleep_time = sleep_time
        self.base_url = "https://www.chipotle.com/#menu"
        self.browser = None
        self.page = None
        self.api_key = None

    async def init(self):
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=self.headless)
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
        # Set up request listener for API key capture
        async def handle_request(request):
            if "ocp-apim-subscription-key" in request.headers:
                self.api_key = request.headers["ocp-apim-subscription-key"]
                print("[INFO] 🔑 API Key captured:", self.api_key)
        
        self.page.on("request", handle_request)

    async def get_store_ids(self, zip_code: str) -> List[str]:
        """Get store IDs for a given zip code"""
        try:
            print("[INFO] Opening Chipotle homepage")
            await self.page.goto(self.base_url, wait_until="domcontentloaded")
            sleep(self.sleep_time)

            print("[INFO] Waiting for 'Find A Chipotle' button")
            find_button = await self.page.wait_for_selector("div.find-a-chipotle")
            if not find_button:
                print("[FATAL] Could not find 'Find A Chipotle' button — aborting.")
                return []
            await find_button.click()
            sleep(self.sleep_time*12)
            await self.accept_cookie_banner()

            try:
                input_box = await self.page.wait_for_selector("input[role='searchbox']")
                await input_box.click()
                await input_box.fill("")
                
                for char in zip_code:
                    await input_box.type(char, delay=1000)
                await self.page.wait_for_timeout(1000)
                
                await input_box.type(" USA", delay=100)
                await self.page.wait_for_timeout(1000)
                await input_box.press("Enter")
                sleep(self.sleep_time*3)
                print(f"[INFO] Entered zip code: {zip_code}")
            except Exception as e:
                print(f"[ERROR] Failed to enter zip code: {e}")
                return {}

            try:
                # Wait for API key capture
                await self.page.wait_for_timeout(5000)
                if not self.api_key:
                    print("[ERROR] Failed to capture API key")
                    return []

                branch_cards = await self.page.query_selector_all("div.cmg-restaurant-address-item")
                if not branch_cards:
                    print(f"[ERROR] No branches found in ZIP {zip_code}")
                    return []
                
                store_ids = []
                print(f"[INFO] Found {len(branch_cards)} branches")
                for branch in branch_cards:
                    store_id = await branch.get_attribute("data-qa-restaurant-id")
                    if store_id:
                        store_ids.append(store_id)
                        print(f"[INFO] Found store ID: {store_id}")
                
                return store_ids

            except Exception as e:
                print(f"[ERROR] Failed to get store IDs: {e}")
                return []

        except Exception as e:
            print(f"[ERROR] Failed to get store IDs: {e}")
            return []

    async def fetch_menu_data(self, store_id: str) -> Dict:
        """Fetch menu data for a store using the API"""
        url = f"https://services.chipotle.com/menuinnovation/v1/restaurants/{store_id}/onlinemenu"
        params = {
            "channelId": "web",
            "includeUnavailableItems": "true"
        }
        headers = {
            "accept": "application/json",
            "chipotle-correlationid": f"OrderWeb-{uuid.uuid4()}",
            "ocp-apim-subscription-key": self.api_key,
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        # Add delay between requests
                        sleep(self.sleep_time * 2)
                        return data
                    else:
                        print(f"[ERROR] Failed to fetch menu for store {store_id}. Status: {response.status}")
                        return {}
        except Exception as e:
            print(f"[ERROR] API request failed for store {store_id}: {e}")
            return {}

    async def crawl_menu(self, zip_code: str, output_file: Optional[str] = None) -> Dict:
        # await self.init()
        try:
            await self.init()
            store_ids = await self.get_store_ids(zip_code)
            
        #     if not store_ids:
        #         print("[ERROR] No store IDs found")
        #         return {}

        #     # Fetch menu data for all stores sequentially
        #     menus = {}
        #     for store_id in store_ids:
        #         print(f"[INFO] Fetching menu for store {store_id}")
        #         menu_data = await self.fetch_menu_data(store_id)
        #         if menu_data:
        #             menus[store_id] = menu_data
        #             print(f"[SUCCESS] Got menu data for store {store_id}")
        #         # Add extra delay between stores
        #         sleep(self.sleep_time * 3)

        #     if output_file and menus:
        #         os.makedirs(os.path.dirname(output_file), exist_ok=True)
        #         with open(output_file, "w", encoding="utf-8") as f:
        #             json.dump(menus, f, indent=2)
        #         print(f"[INFO] Menu data saved to {output_file}")

        #     return menus

        except Exception as e:
            print(f"[ERROR] Crawl failed: {e}")
            return {}
        finally:
            await self.close()

    async def close(self):
        if self.browser:
            await self.browser.close()

async def run_crawler(zip_code: str, output_file: Optional[str] = None, headless: bool = True, sleep_time: int = 5) -> Dict:
    crawler = ChipotlePlaywrightCrawler(headless=headless, sleep_time=sleep_time)
    return await crawler.crawl_menu(zip_code, output_file)

if __name__ == "__main__":
    import asyncio
    zip_code = "10004"
    output_file = "output/chipotle_menu.json"
    asyncio.run(run_crawler(zip_code, output_file, headless=False, sleep_time=5)) 