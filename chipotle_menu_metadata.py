#!/usr/bin/env python3
"""
<PERSON>ript to fetch Chipotle menu metadata.
This provides additional information about menu items, ingredients, and nutritional data.
"""

import json
import requests
import sys
import argparse

def fetch_menu_metadata():
    """
    Send a request to the Chipotle API to get menu metadata.
    Transform and save the data in the format matching test.json.
    """
    url = "https://services.chipotle.com/menu-metadata/v1/menu-metadata"

    # Query parameters
    params = {
        "channel": "web",
        "region": "US"
    }

    # Headers from the curl command
    headers = {
        "x-kpsdk-ct": "0HKt5Z9aTcNcFzMGbf8jE1ovlwu6xQWo5T7g5JgJspZdbSQgah2B6Qu5crdiRLqET1XdwsVE66oLYBneQpP1llXgJX8DHVZh0eHJiFBDMFB7C2CY2gIs9GdlvNd1qJ20zEJJeOha7DW1meJTULEsV9z58LO6IX80b5RNHfUF",
        "sec-ch-ua-platform": "\"Android\"",
        "Referer": "https://www.chipotle.com/",
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?1",
        "x-kpsdk-v": "j-1.1.0",
        "Chipotle-CorrelationId": "OrderWeb-bb874df9-fec1-4c3f-88b6-59941e4dd2b4",
        "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
        "Accept": "application/json, text/plain, */*",
        "DNT": "1",
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
    }

    try:
        print("Sending request to Chipotle API for menu metadata...")
        response = requests.get(url, headers=headers, params=params)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the JSON response
        original_data = response.json()

        # Transform the data to match the format in test.json
        transformed_data = {"items": {}}

        # First, check if we have items in the data (as seen in the current menu_metadata.json)
        if "items" in original_data and isinstance(original_data["items"], dict):
            for item_id, item_data in original_data["items"].items():
                # Create an entry for each item with the required fields
                transformed_data["items"][item_id] = {}

                # Add thumbnailUrl if available
                if "thumbnailUrl" in item_data:
                    transformed_data["items"][item_id]["thumbnailUrl"] = item_data["thumbnailUrl"]

                # Add description if available
                if "description" in item_data:
                    transformed_data["items"][item_id]["description"] = item_data["description"]

                # Add bannerUrl if available
                if "bannerUrl" in item_data:
                    transformed_data["items"][item_id]["bannerUrl"] = item_data["bannerUrl"]

        # If no items found in the expected format, try to extract from groups
        elif "groups" in original_data:
            # Process each group to find items with thumbnailImageUrl
            for group in original_data["groups"]:
                # Get group thumbnail and banner URLs for reference
                group_thumbnail = group.get("thumbnailImageUrl", "")
                group_banner = group.get("bannerImageUrl", "")

                # Process items in this group
                if "items" in group and isinstance(group["items"], list):
                    for item_entry in group["items"]:
                        if "menuItemId" in item_entry:
                            item_id = item_entry["menuItemId"]
                            # Only add if not already in the transformed data
                            if item_id not in transformed_data["items"]:
                                transformed_data["items"][item_id] = {}

                                # For now, use the group's thumbnail URL as a fallback
                                if group_thumbnail:
                                    transformed_data["items"][item_id]["thumbnailUrl"] = group_thumbnail

                                # Use group's banner URL as a fallback
                                if group_banner:
                                    transformed_data["items"][item_id]["bannerUrl"] = group_banner

        # Create the filename
        filename = "menu_metadata.json"

        # Save the transformed response to a JSON file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(transformed_data, f, indent=4)

        print(f"Successfully saved transformed menu metadata to {filename}")

        # Print some basic info about the metadata
        if "items" in transformed_data:
            print(f"Transformed metadata contains information for {len(transformed_data['items'])} menu items")
        else:
            print("Transformed data structure is empty, check the original response")

        # Also save the original data for reference
        with open("menu_metadata_original.json", 'w', encoding='utf-8') as f:
            json.dump(original_data, f, indent=4)

        print(f"Also saved original metadata to menu_metadata_original.json")

        return True

    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return False

    except json.JSONDecodeError:
        print("Error: Could not parse the response as JSON")
        return False

    except IOError as e:
        print(f"Error saving file: {e}")
        return False

def search_item_in_metadata(item_id=None, item_name=None):
    """
    Search for a specific item in the metadata by ID or name.
    This function can be used after the metadata has been fetched.
    Works with the new format that matches test.json.
    """
    try:
        # Load the transformed metadata file
        with open("menu_metadata.json", 'r', encoding='utf-8') as f:
            transformed_data = json.load(f)

        # Load the original metadata file for additional information
        try:
            with open("menu_metadata_original.json", 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            has_original_data = True
        except (FileNotFoundError, json.JSONDecodeError):
            has_original_data = False
            original_data = {}

        # Check if we have items in the transformed metadata
        if "items" in transformed_data and isinstance(transformed_data["items"], dict):
            found = False

            # If searching by ID, directly check if it exists in the items dictionary
            if item_id and item_id in transformed_data["items"]:
                found = True
                item_data = transformed_data["items"][item_id]

                print(f"\nFound item in transformed data:")
                print(f"  ID: {item_id}")
                print(f"  Thumbnail URL: {item_data.get('thumbnailUrl', 'Not available')}")

                if "description" in item_data:
                    print(f"  Description: {item_data['description']}")

                if "bannerUrl" in item_data:
                    print(f"  Banner URL: {item_data['bannerUrl']}")

                # If we have the original data, we can show more information
                if has_original_data and "menuItems" in original_data and item_id in original_data["menuItems"]:
                    orig_item = original_data["menuItems"][item_id]

                    if "name" in orig_item:
                        print(f"  Name: {orig_item['name']}")

                    # Print nutritional info if available
                    if "nutritionalInfo" in orig_item:
                        print("  Nutritional Information:")
                        for key, value in orig_item["nutritionalInfo"].items():
                            print(f"    {key}: {value}")

                    # Print ingredients if available
                    if "ingredients" in orig_item:
                        print("  Ingredients:")
                        for ingredient in orig_item["ingredients"]:
                            print(f"    - {ingredient}")

            # If searching by name and we have original data, search through it
            elif item_name and has_original_data and "menuItems" in original_data:
                for orig_id, orig_item in original_data["menuItems"].items():
                    if item_name.lower() in orig_item.get("name", "").lower():
                        found = True

                        print(f"\nFound item by name in original data:")
                        print(f"  ID: {orig_id}")
                        print(f"  Name: {orig_item.get('name', 'Unknown')}")

                        # Check if this item exists in the transformed data
                        if orig_id in transformed_data["items"]:
                            item_data = transformed_data["items"][orig_id]
                            print(f"  Thumbnail URL: {item_data.get('thumbnailUrl', 'Not available')}")

                            if "description" in item_data:
                                print(f"  Description: {item_data['description']}")

                            if "bannerUrl" in item_data:
                                print(f"  Banner URL: {item_data['bannerUrl']}")

                        # Print additional information from original data
                        if "description" in orig_item:
                            print(f"  Description: {orig_item['description']}")

                        # Print nutritional info if available
                        if "nutritionalInfo" in orig_item:
                            print("  Nutritional Information:")
                            for key, value in orig_item["nutritionalInfo"].items():
                                print(f"    {key}: {value}")

                        # Print ingredients if available
                        if "ingredients" in orig_item:
                            print("  Ingredients:")
                            for ingredient in orig_item["ingredients"]:
                                print(f"    - {ingredient}")

            if not found:
                if item_id:
                    print(f"\nItem with ID {item_id} not found in the metadata")
                elif item_name:
                    print(f"\nNo items matching '{item_name}' found in the metadata")
        else:
            print("Could not find items in the expected format in the metadata")

        return True

    except FileNotFoundError:
        print("Error: menu_metadata.json file not found. Run the script without arguments first to fetch the metadata.")
        return False

    except json.JSONDecodeError:
        print("Error: Could not parse the metadata file as JSON")
        return False

if __name__ == "__main__":
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(description='Fetch Chipotle menu metadata and search for specific items')
    parser.add_argument('--item-id', type=str, help='Search for a specific item by ID (e.g., CMG-1115)')
    parser.add_argument('--item-name', type=str, help='Search for items by name (partial match)')
    parser.add_argument('--fetch-only', action='store_true', help='Only fetch the metadata without searching')

    # Parse the arguments
    args = parser.parse_args()

    # If no search parameters are provided or fetch-only is specified, just fetch the metadata
    if args.fetch_only or (not args.item_id and not args.item_name):
        success = fetch_menu_metadata()
    # If we already have the metadata file and want to search in it
    elif args.item_id or args.item_name:
        success = search_item_in_metadata(args.item_id, args.item_name)

    sys.exit(0 if success else 1)