#!/usr/bin/env python3
"""
Script to fetch Chipotle menu data for a specific restaurant.
Usage: python chipotle_menu.py --restaurant-number 4636
"""

import json
import requests
import sys
import argparse

def fetch_chipotle_menu(restaurant_number):
    """
    Send a request to the Chipotle API to get menu data for a specific restaurant.
    """
    if not restaurant_number:
        print("Error: Restaurant number is required")
        return False

    url = f"https://services.chipotle.com/menuinnovation/v1/restaurants/{restaurant_number}/onlinemenu"

    # Query parameters
    params = {
        "channelId": "web",
        "includeUnavailableItems": "true"
    }

    # Headers from the curl command
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Chipotle-CorrelationId": "OrderWeb-00227017-1f3d-431b-945c-128db966dd09",
        "Connection": "keep-alive",
        "DNT": "1",
        "Ocp-Apim-Subscription-Key": "b4d9f36380184a3788857063bce25d6a",
        "Origin": "https://www.chipotle.com",
        "Referer": "https://www.chipotle.com/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": "\"Android\"",
        "x-kpsdk-ct": "0F8T1iNJH1rIUvBuThQ1vrc7InDZxcnmfTVq9sz4k47tpWbW1pl4w5pCBoKb7nQ3AAOySbkH7FWfn41wURQJbiwsnFbrJosRHwEgpbdkkqdpqaHmnZGenbxDkULvosxNml7xLhp3RHw3BuWdlTtnk2jW3neCvHbrMYTh2n0f",
        "x-kpsdk-v": "j-1.1.0"
    }

    try:
        print(f"Sending request to Chipotle API for restaurant #{restaurant_number}...")
        response = requests.get(url, headers=headers, params=params)

        # Check if the request was successful
        response.raise_for_status()

        # Parse the JSON response
        original_data = response.json()

        # Transform the data to match the desired format
        transformed_data = {
            "restaurantId": original_data.get("restaurantId")
        }

        # Process each section (entrees, sides, drinks, nonFoodItems)
        for section in ["entrees", "sides", "drinks", "nonFoodItems"]:
            if section in original_data:
                # Create a list to hold the simplified items for this section
                transformed_data[section] = []

                # Process each item in the section
                for item in original_data[section]:
                    # Extract only the fields we need
                    simplified_item = {
                        "itemName": item.get("itemName", ""),
                        "itemId": item.get("itemId", ""),
                        "itemCategory": item.get("itemCategory", ""),
                        "itemType": item.get("itemType", ""),
                        "unitPrice": item.get("unitPrice", 0),
                        "unitCount": item.get("unitCount", 1)
                    }

                    # Add primaryFillingName for entrees if available
                    if section == "entrees" and "primaryFillingName" in item:
                        simplified_item["primaryFillingName"] = item["primaryFillingName"]

                    # Add the simplified item to the section
                    transformed_data[section].append(simplified_item)

        # Create the filename with the restaurant number
        filename = f"menu_{restaurant_number}.json"

        # Save the transformed response to a JSON file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(transformed_data, f, indent=4)

        print(f"Successfully saved menu to {filename}")

        # Print some basic info about the menu
        menu_sections = ["entrees", "sides", "drinks", "nonFoodItems"]
        print(f"Menu for restaurant #{transformed_data.get('restaurantId', restaurant_number)}:")

        for section in menu_sections:
            if section in transformed_data:
                print(f"  - {section.capitalize()}: {len(transformed_data[section])} items")

                # Print the first 3 items in each section as an example
                for i, item in enumerate(transformed_data[section][:3]):
                    name = item.get('itemName', 'Unknown')
                    price = item.get('unitPrice', 'N/A')
                    print(f"      {i+1}. {name} - ${price if price != 'N/A' else 'N/A'}")

                if len(transformed_data[section]) > 3:
                    print(f"      ... and {len(transformed_data[section]) - 3} more items")

        return True

    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return False

    except json.JSONDecodeError:
        print("Error: Could not parse the response as JSON")
        return False

    except IOError as e:
        print(f"Error saving file: {e}")
        return False

def main():
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(description='Fetch Chipotle menu data for a specific restaurant')
    parser.add_argument('--restaurant-number', type=str, required=True,
                        help='Chipotle restaurant number to fetch menu for')

    # Parse the arguments
    args = parser.parse_args()

    # Call the function with the restaurant number
    success = fetch_chipotle_menu(args.restaurant_number)

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
